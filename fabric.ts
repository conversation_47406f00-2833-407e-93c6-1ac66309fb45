const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  console.log('Начинаем заполнение базы данных демоданными...')

  // Создаем клиентов
  const clients = await Promise.all([
    prisma.client.create({
      data: {
        fullName: 'Иванов Иван Иванович',
        phone: '+7 (495) 123-45-67',
        email: 'i<PERSON><EMAIL>',
      },
    }),
    prisma.client.create({
      data: {
        fullName: 'Петрова Мария Сергеевна',
        phone: '+7 (495) 234-56-78',
        email: '<EMAIL>',
      },
    }),
    prisma.client.create({
      data: {
        fullName: 'Сидоров Алексей Владимирович',
        phone: '+7 (495) 345-67-89',
        email: '<PERSON><PERSON><PERSON>@example.com',
      },
    }),
    prisma.client.create({
      data: {
        fullName: 'Ко<PERSON>л<PERSON><PERSON> Елена Александровна',
        phone: '+7 (495) 456-78-90',
        email: '<EMAIL>',
      },
    }),
  ])

  console.log('Клиенты созданы:', clients.length)

  // Создаем дела
  const cases = await Promise.all([
    prisma.caze.create({
      data: {
        title: 'Раздел имущества при разводе',
        description: 'Раздел совместно нажитого имущества супругов, включая квартиру и автомобиль',
        clientId: clients[0].id,
      },
    }),
    prisma.caze.create({
      data: {
        title: 'Трудовой спор с работодателем',
        description: 'Незаконное увольнение и взыскание задолженности по заработной плате',
        clientId: clients[1].id,
      },
    }),
    prisma.caze.create({
      data: {
        title: 'Наследственное дело',
        description: 'Оформление наследства после смерти родственника, спор с другими наследниками',
        clientId: clients[2].id,
      },
    }),
    prisma.caze.create({
      data: {
        title: 'ДТП и страховые выплаты',
        description: 'Взыскание ущерба с виновника ДТП и страховой компании',
        clientId: clients[3].id,
      },
    }),
  ])

  console.log('Дела созданы:', cases.length)

  // Создаем отзывы
  const reviews = await Promise.all([
    prisma.review.create({
      data: {
        clientName: 'Анна Михайловна',
        rating: 5,
        comment: 'Отличный юрист! Помог решить сложный семейный спор. Профессионально и быстро.',
        approved: true,
      },
    }),
    prisma.review.create({
      data: {
        clientName: 'Дмитрий Николаевич',
        rating: 5,
        comment: 'Благодарю за качественную юридическую помощь. Все вопросы решены в мою пользу.',
        approved: true,
      },
    }),
    prisma.review.create({
      data: {
        clientName: 'Светлана Петровна',
        rating: 4,
        comment: 'Хороший специалист, но процесс затянулся дольше ожидаемого.',
        approved: true,
      },
    }),
    prisma.review.create({
      data: {
        clientName: 'Владимир Сергеевич',
        rating: 5,
        comment: 'Рекомендую! Грамотный подход к делу и разумные цены.',
        approved: false,
      },
    }),
  ])

  console.log('Отзывы созданы:', reviews.length)

  // Создаем консультации
  const consultations = await Promise.all([
    prisma.consultation.create({
      data: {
        name: 'Ольга Викторовна',
        phone: '+7 (495) 567-89-01',
        text: 'Нужна консультация по вопросам алиментов на детей после развода.',
      },
    }),
    prisma.consultation.create({
      data: {
        name: 'Максим Андреевич',
        phone: '+7 (495) 678-90-12',
        text: 'Требуется помощь в оформлении договора купли-продажи недвижимости.',
      },
    }),
    prisma.consultation.create({
      data: {
        name: 'Татьяна Игоревна',
        phone: '+7 (495) 789-01-23',
        text: 'Вопрос по защите прав потребителей, некачественный товар в магазине.',
      },
    }),
  ])

  console.log('Консультации созданы:', consultations.length)

  // Создаем судебные заседания
  const courtSessions = await Promise.all([
    prisma.courtSession.create({
      data: {
        sessionNumber: '2-1234/2024',
        sessionName: 'Гражданское дело по иску Иванова И.И.',
        startTime: new Date('2024-02-15T10:00:00'),
        endTime: new Date('2024-02-15T12:00:00'),
      },
    }),
    prisma.courtSession.create({
      data: {
        sessionNumber: '2-5678/2024',
        sessionName: 'Трудовой спор Петровой М.С.',
        startTime: new Date('2024-02-20T14:00:00'),
        endTime: new Date('2024-02-20T16:00:00'),
      },
    }),
    prisma.courtSession.create({
      data: {
        sessionNumber: '2-9012/2024',
        sessionName: 'Наследственное дело Сидорова А.В.',
        startTime: new Date('2024-02-25T09:00:00'),
        endTime: new Date('2024-02-25T11:00:00'),
      },
    }),
  ])

  console.log('Судебные заседания созданы:', courtSessions.length)

  // Создаем статьи
  const articles = await Promise.all([
    prisma.article.create({
      data: {
        title: 'Как правильно оформить развод в 2024 году',
        content: 'Развод — это сложная юридическая процедура, которая требует знания множества нюансов. В данной статье мы рассмотрим основные этапы расторжения брака, необходимые документы и возможные подводные камни...',
        authorName: 'Юрист Александр Петров',
        published: true,
      },
    }),
    prisma.article.create({
      data: {
        title: 'Права работника при увольнении',
        content: 'Каждый работник должен знать свои права при увольнении. Работодатель не может уволить сотрудника без веских оснований. Рассмотрим основные случаи правомерного и неправомерного увольнения...',
        authorName: 'Юрист Мария Иванова',
        published: true,
      },
    }),
    prisma.article.create({
      data: {
        title: 'Наследование по закону и по завещанию',
        content: 'Наследственное право — одна из самых сложных областей юриспруденции. В статье разберем различия между наследованием по закону и по завещанию, а также порядок вступления в наследство...',
        authorName: 'Юрист Сергей Николаев',
        published: false,
      },
    }),
    prisma.article.create({
      data: {
        title: 'Защита прав потребителей: что нужно знать',
        content: 'Права потребителей защищены законом, но многие не знают, как правильно их отстаивать. В статье рассмотрим основные права покупателей и способы их защиты при нарушении...',
        authorName: 'Юрист Елена Смирнова',
        published: true,
      },
    }),
  ])

  console.log('Статьи созданы:', articles.length)

  console.log('База данных успешно заполнена демоданными!')
}

main()
  .catch((e) => {
    console.error('Ошибка при заполнении базы данных:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
