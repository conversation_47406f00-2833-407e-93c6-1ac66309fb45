---
import "../../styles/global.css";
import { ClientRouter } from "astro:transitions";
import Navigation from "../components/Navigation.astro";
---

<!doctype html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>Astro Basics</title>
    <ClientRouter />
  </head>
  <body class="min-h-screen">
    <Navigation />
    <main class="max-w-6xl mx-auto px-4 py-8">
      <slot />
    </main>
  </body>
</html>

<style>
  html,
  body {
    margin: 0;
    width: 100%;
    height: 100%;
  }
</style>
