---
import Layout from "@/layouts/Layout.astro";

const services = [
  {
    name: "Юридическая консультация",
    price: "1000 ₽",
    description: "Профессиональная консультация по любым правовым вопросам",
    lawyer: "Медведев Сергей Иванович"
  },
  {
    name: "Составление документов",
    price: "от 5000 ₽",
    description: "Подготовка договоров, исков, заявлений и других юридических документов",
    lawyer: "<PERSON>етров<PERSON> Анна Сергеевна"
  },
  {
    name: "Представительство в суде",
    price: "от 10000 ₽",
    description: "Полное сопровождение судебного процесса на всех стадиях",
    lawyer: "<PERSON>и<PERSON><PERSON><PERSON><PERSON> Петр Александрович"
  },
  {
    name: "<PERSON><PERSON><PERSON>поративное право",
    price: "от 15000 ₽",
    description: "Регистрация компаний, внесение изменений, корпоративные споры",
    lawyer: "Петрова Анна Сергеевна"
  },
  {
    name: "Семейные споры",
    price: "от 8000 ₽",
    description: "Развод, раздел имущества, алименты, опека",
    lawyer: "Кузнецова Мария Ивановна"
  },
  {
    name: "Уголовные дела",
    price: "от 20000 ₽",
    description: "Защита на следствии и в суде, обжалование приговоров",
    lawyer: "Васильев Дмитрий Николаевич"
  },
  {
    name: "Трудовые споры",
    price: "от 7000 ₽",
    description: "Восстановление на работе, взыскание задолженности по зарплате",
    lawyer: "Новикова Елена Викторовна"
  },
  {
    name: "Административные дела",
    price: "от 5000 ₽",
    description: "Представление интересов в государственных органах",
    lawyer: "Михайлов Андрей Сергеевич"
  },
  {
    name: "Недвижимость",
    price: "от 12000 ₽",
    description: "Сделки с недвижимостью, оформление прав собственности",
    lawyer: "Медведев Сергей Иванович"
  },
  {
    name: "Банкротство физлиц",
    price: "от 25000 ₽",
    description: "Полное сопровождение процедуры банкротства",
    lawyer: "Сидоров Петр Александрович"
  },
  {
    name: "Защита прав потребителей",
    price: "от 4000 ₽",
    description: "Помощь в спорах с продавцами и поставщиками услуг",
    lawyer: "Новикова Елена Викторовна"
  },
  {
    name: "Наследственные дела",
    price: "от 8000 ₽",
    description: "Оформление наследства, споры о наследстве",
    lawyer: "Кузнецова Мария Ивановна"
  },
];
---

<Layout title="Услуги">
  <div class="container mx-auto px-4 py-12">
    <h1 class="text-center text-4xl font-bold mb-12">Юридические услуги</h1>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {services.map((service) => (
        <div class="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 p-6">
          <h3 class="text-xl font-semibold text-gray-800 mb-2">{service.name}</h3>
          <p class="text-gray-600 mb-4">{service.description}</p>
          <div>
            <span class="text-base font-bold text-green-600">{service.price}</span>
          </div>
          <a href={`/lawyers#${service.lawyer}`} class=" mt-5 text-blue-600 hover:text-blue-800 transition-colors">
              {service.lawyer} →
            </a>
        </div>
      ))}
    </div>
  </div>
</Layout>
