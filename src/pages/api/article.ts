import { PrismaClient } from "@prisma/client";
import type { APIRoute } from "astro";

export const prerender = false;

const prisma = new PrismaClient();

export const POST: APIRoute = async ({ request }) => {
  if (!request.body) {
    return new Response(JSON.stringify({ error: 'Empty request body' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const jsonData = await request.json();
  
  try {
    await prisma.article.create({
      data: {
        title: jsonData.title,
        content: jsonData.content,
        authorName: jsonData.author,
        published: true
      }
    });

    return new Response(null, { 
      status: 303,
      headers: { Location: '/articles' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка сохранения статьи' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
