import { PrismaClient } from "@prisma/client";
import type { APIRoute } from "astro";

export const prerender = false;

const prisma = new PrismaClient();

export const POST: APIRoute = async ({ request }) => {
  if (!request.body) {
    return new Response(JSON.stringify({ error: 'Empty request body' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const jsonData = await request.json();

  try {
    const res = await prisma.consultation.create({
      data: {
        name: jsonData.name,
        phone: jsonData.phone,
        text: jsonData.text
      }
    });

    console.log('create consultation res:', res)
    return new Response(null, {
      status: 303,
      headers: { Location: '/' }
    });
  } catch (error) {
    console.error('error:', error)
    return new Response(JSON.stringify({ error: 'Ошибка сохранения' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};