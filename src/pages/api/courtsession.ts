import { PrismaClient } from "@prisma/client";
import type { APIRoute } from "astro";

export const prerender = false;

const prisma = new PrismaClient();

export const POST: APIRoute = async ({ request }) => {

    const jsonData = await request.json();

    try {
        const res = await prisma.courtSession.create({
            data: {
                startTime: jsonData.startTime,
                endTime: jsonData.endTime,
                sessionName: jsonData.sessionName,
                sessionNumber: jsonData.sessionNumber
            }
        })

        console.log('create review res:', res)

        return new Response(null, {
            status: 201,
        });
    } catch (error) {
        return new Response(JSON.stringify({ error: 'Ошибка сохранения' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
};