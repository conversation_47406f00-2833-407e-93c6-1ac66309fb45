import { PrismaClient } from "@prisma/client";
import type { APIRoute } from "astro";

export const prerender = false;

const prisma = new PrismaClient();

export const POST: APIRoute = async ({ request }) => {
  if (!request.body) {
    return new Response(JSON.stringify({ error: 'Empty request body' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const jsonData = await request.json();
  
  try {
    const res = await prisma.review.create({
      data: {
        clientName: jsonData.name,
        rating: parseInt(jsonData.rating),
        comment: jsonData.comment,
        approved: true // премодерация отключена
      }
    });

    console.log('create review res:', res)
    return new Response(null, { 
      status: 303,
      headers: { Location: '/reviews' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка сохранения' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};