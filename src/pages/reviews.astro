---
import Layout from "@/layouts/Layout.astro";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();
const reviews = await prisma.review.findMany({
  where: { approved: true },
  orderBy: { createdAt: "desc" },
});

function transformDate(date) {
  return new Date(date).toLocaleDateString("ru-RU", {
    day: "2-digit",
    month: "2-digit", 
    year: "numeric",
  });
}
---

<Layout title="Отзывы">
  <section
    class="max-w-4xl mx-auto grid gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
  >
    {
      reviews.map((review) => (
        <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <h3 class="text-lg font-semibold text-gray-800 mb-1">
            {review.clientName}
          </h3>
          <div class="flex items-center justify-between mb-2">
            <div class="text-yellow-500">
              Оценка: {"★".repeat(review.rating)}
            </div>
            <span class="text-gray-500 text-sm">
              {transformDate(review.createdAt)}
            </span>
          </div>{" "}
          <p class="text-gray-700 leading-relaxed">{review.comment}</p>
        </div>
      ))
    }
  </section>

  <form
    id="reviewForm"
    class="mt-10 max-w-2xl mx-auto bg-white p-8 rounded-lg shadow-xl mb-12 border border-gray-200"
  >
    <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">
      Оставьте свой отзыв
    </h2>

    <div class="space-y-6">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1"
          >Ваше имя</label
        >
        <input
          type="text"
          name="name"
          required
          id="name"
          class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Иван Иванов"
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1"
          >Оценка</label
        >
        <select
          name="rating"
          required
          id="rating"
          class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="5">★★★★★ - Отлично</option>
          <option value="4">★★★★☆ - Хорошо</option>
          <option value="3">★★★☆☆ - Удовлетворительно</option>
          <option value="2">★★☆☆☆ - Плохо</option>
          <option value="1">★☆☆☆☆ - Ужасно</option>
        </select>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1"
          >Ваш отзыв</label
        >
        <textarea
          name="comment"
          rows="4"
          required
          id="comment"
          class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Поделитесь своими впечатлениями..."></textarea>
      </div>
    </div>

    <button
      id="formSubmitBtn"
      type="submit"
      class="mt-6 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
    >
      Отправить отзыв
    </button>
  </form>
</Layout>

<script>

  const form = document.getElementById("reviewForm");
  form?.addEventListener("submit", async (event) => {
    event.preventDefault();

    const formData = new FormData(form);

    const jsonData = {
      name: formData.get("name"),
      rating: formData.get("rating"),
      comment: formData.get("comment"),
    };

    const response = await fetch("/api/review", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(jsonData),
    });

    if (response.ok) {
      window.location.reload();
    }
  });
</script>
