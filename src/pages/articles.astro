---
import Layout from "@/layouts/Layout.astro";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();
const articles = await prisma.article.findMany({
  where: { published: true },
  orderBy: { createdAt: "desc" },
});

function transformDate(date) {
  return new Date(date).toLocaleDateString("ru-RU", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric"
  });
}
---

<Layout title="Статьи">
  <section class="max-w-4xl mx-auto grid gap-8">
    {articles.map((article) => (
      <article class="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow">
        <h2 class="text-2xl font-semibold text-gray-800 mb-2">{article.title}</h2>
        <div class="flex items-center justify-between mb-4">
          <span class="text-gray-600">Автор: {article.authorName}</span>
          <span class="text-gray-500 text-sm">{transformDate(article.createdAt)}</span>
        </div>
        <p class="text-gray-700 leading-relaxed">{article.content}</p>
      </article>
    ))}
  </section>

  <form
    id="addArticleForm"
    class="mt-10 max-w-2xl mx-auto bg-white p-8 rounded-lg shadow-xl mb-12 border border-gray-200"
  >
    <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">
      Добавить новую статью
    </h2>

    <div class="space-y-6">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Заголовок:</label>
        <input
          type="text"
          name="title"
          required
          class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Содержание:</label>
        <textarea
          name="content"
          required
          rows="4"
          class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Автор:</label>
        <input
          type="text"
          name="author"
          required
          class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
    </div>

    <button
      type="submit"
      class="mt-6 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
    >
      Добавить статью
    </button>
  </form>
</Layout>

<script>
  const form = document.getElementById('addArticleForm');
  form?.addEventListener('submit', async (event) => {
    event.preventDefault();

    const formData = new FormData(form);
    const jsonData = {
      title: formData.get('title'),
      content: formData.get('content'),
      author: formData.get('author')
    };

    const response = await fetch('/api/article', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(jsonData)
    });

    if (response.ok) {
      window.location.reload();
    }
  });
</script>
