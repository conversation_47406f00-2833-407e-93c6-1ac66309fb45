---
import Layout from '../layouts/Layout.astro';

const highlights = [
  {
    number: "15+",
    text: "лет успешной практики"
  },
  {
    number: "1000+",
    text: "выигранных дел"
  },
  {
    number: "8",
    text: "профессиональных юристов"
  },
  {
    number: "24/7",
    text: "поддержка клиентов"
  }
];

const advantages = [
  {
    title: "Профессионализм",
    description: "Команда опытных юристов с профильным образованием и обширной практикой",
    icon: "⚖️"
  },
  {
    title: "Конфиденциальность",
    description: "Гарантируем полную защиту ваших персональных данных и деталей дела",
    icon: "🔒"
  },
  {
    title: "Оперативность",
    description: "Быстрое реагирование на запросы и соблюдение всех процессуальных сроков",
    icon: "⚡"
  },
  {
    title: "Результативность",
    description: "97% дел решаются в пользу наших клиентов",
    icon: "🎯"
  }
];
---

<Layout title="Юридическая компания">
  <main>
    <!-- Hero Section -->
    <section class="relative h-screen rounded-xl flex items-center justify-center bg-linear-to-r from-cyan-500 to-sky-800 text-white">
      <div class="container mx-auto px-4 z-10 text-center">
        <h1 class="text-5xl md:text-7xl font-bold mb-6">Ваше право на защиту</h1>
        <p class="text-xl md:text-2xl mb-8">Профессиональная юридическая помощь в любых ситуациях</p>
        <div class="flex gap-4 justify-center">
          <a href="/services" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg transition duration-300">
            Наши услуги
          </a>
          <a href="#consultation" class="bg-transparent border-2 border-white hover:bg-white hover:text-gray-900 text-white px-8 py-3 rounded-lg transition duration-300">
            Консультация
          </a>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          {highlights.map(item => (
            <div class="text-center">
              <div class="text-4xl font-bold text-green-600 mb-2">{item.number}</div>
              <div class="text-gray-600">{item.text}</div>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Services Preview -->
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-16">Основные направления</h2>
        <div class="grid md:grid-cols-3 gap-8">
          <div class="bg-white p-8 rounded-lg shadow-lg">
            <h3 class="text-xl font-semibold mb-4">Гражданские дела</h3>
            <p class="text-gray-600 mb-4">Разрешение споров, защита прав потребителей, взыскание задолженности</p>
            <a href="/services" class="text-green-600 hover:text-green-700">Подробнее →</a>
          </div>
          <div class="bg-white p-8 rounded-lg shadow-lg">
            <h3 class="text-xl font-semibold mb-4">Уголовные дела</h3>
            <p class="text-gray-600 mb-4">Защита на всех стадиях уголовного процесса, обжалование решений</p>
            <a href="/services" class="text-green-600 hover:text-green-700">Подробнее →</a>
          </div>
          <div class="bg-white p-8 rounded-lg shadow-lg">
            <h3 class="text-xl font-semibold mb-4">Корпоративное право</h3>
            <p class="text-gray-600 mb-4">Сопровождение бизнеса, регистрация компаний, правовой аудит</p>
            <a href="/services" class="text-green-600 hover:text-green-700">Подробнее →</a>
          </div>
        </div>
      </div>
    </section>

    <!-- Advantages -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-16">Почему выбирают нас</h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {advantages.map(adv => (
            <div class="text-center">
              <div class="text-4xl mb-4">{adv.icon}</div>
              <h3 class="text-xl font-semibold mb-2">{adv.title}</h3>
              <p class="text-gray-600">{adv.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Consultation Form -->
    <section id="consultation" class="py-20 bg-gray-900 rounded-xl text-white">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-16">Получить консультацию</h2>
        <form id="consultationForm" class="max-w-lg mx-auto">
          <div class="mb-6">
            <input name="name" type="text" placeholder="Ваше имя" class="w-full px-4 py-3 rounded-lg bg-gray-800 text-white border border-gray-700 focus:border-green-500 focus:outline-none">
          </div>
          <div class="mb-6">
            <input name="phone" type="tel" placeholder="Телефон" class="w-full px-4 py-3 rounded-lg bg-gray-800 text-white border border-gray-700 focus:border-green-500 focus:outline-none">
          </div>
          <div class="mb-6">
            <textarea name="text" placeholder="Опишите ваш вопрос" rows="4" class="w-full px-4 py-3 rounded-lg bg-gray-800 text-white border border-gray-700 focus:border-green-500 focus:outline-none"></textarea>
          </div>
          <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg transition duration-300">
            Отправить
          </button>
        </form>
      </div>
    </section>
  </main>
</Layout>

<script>

	const form = document.getElementById("consultationForm");
	form?.addEventListener("submit", async (event) => {
	  event.preventDefault();
  
	  const formData = new FormData(form);
  
	  const jsonData = {
		name: formData.get("name"),
		phone: formData.get("phone"),
		text: formData.get("text"),
	  };
  
	  const response = await fetch("/api/consultation", {
		method: "POST",
		headers: {
		  "Content-Type": "application/json",
		},
		body: JSON.stringify(jsonData),
	  });
  
	  if (response.ok) {
		alert('Спасибо за обращение! Мы свяжемся с вами в ближайшее время.')
	  }
	});
  </script>
