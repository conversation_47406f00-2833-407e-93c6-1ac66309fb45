---
import { PrismaClient } from "@prisma/client";
import Layout from "../layouts/Layout.astro";

const prisma = new PrismaClient();
const courtSessions = await prisma.courtSession.findMany({
  orderBy: { startTime: 'desc' }
});

function calcduration(session) {
  const startTime = new Date(session.startTime).getTime();
  const endTime = new Date(session.endTime).getTime();
  const durationInMinutes = Math.round((endTime - startTime) / (1000 * 60));
  return durationInMinutes;
}
---

<Layout title="Судебные заседания">
  <main class="mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <h1 class="text-3xl font-bold text-gray-800">Судебные заседания</h1>
      <a 
        href="/timer" 
        class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Новое заседание
      </a>
    </div>

    <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {courtSessions.map((session) => (
        <article class="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">{session.sessionName}</h2>
          <div class="space-y-2 text-gray-600">
            <p class="flex items-center gap-2">
              <span class="font-medium">Номер дела:</span>
              <span>{session.sessionNumber}</span>
            </p>
            <p class="flex items-center gap-2">
              <span class="font-medium">Начало:</span>
              <span>{new Date(session.startTime).toLocaleString("ru-RU")}</span>
            </p>
            <p class="flex items-center gap-2">
              <span class="font-medium">Окончание:</span>
              <span>{new Date(session.endTime).toLocaleString("ru-RU")}</span>
            </p>
            <p class="flex items-center gap-2">
              <span class="font-medium">Длительность:</span>
              <span>{calcduration(session)} мин.</span>
            </p>
          </div>
        </article>
      ))}
    </section>
  </main>
</Layout>
