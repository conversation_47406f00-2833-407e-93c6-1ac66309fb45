import * as React from "react"
import { cn } from "@/lib/utils"

interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number
  max?: number
  showValue?: boolean
  size?: "sm" | "md" | "lg"
}

const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
  ({ className, value, max = 100, showValue = false, size = "md", ...props }, ref) => {
    const percentage = (Math.min(Math.max(value, 0), max) / max) * 100

    return (
      <div
        ref={ref}
        className={cn(
          "w-full overflow-hidden rounded-full bg-gray-200",
          {
            "h-2": size === "sm",
            "h-4": size === "md",
            "h-6": size === "lg",
          },
          className,
        )}
        {...props}
      >
        <div
          className="h-full w-full flex-1 bg-gray-800 transition-all"
          style={{ transform: `translateX(-${100 - percentage}%)` }}
        >
          {showValue && size !== "sm" && (
            <div className="flex h-full items-center justify-center">
              <span className="text-xs font-medium text-primary-foreground">{Math.round(percentage)}%</span>
            </div>
          )}
        </div>
      </div>
    )
  },
)

Progress.displayName = "Progress"

export { Progress }

