---
const currentPath = Astro.url.pathname;
---

<nav class="bg-gray-100 shadow-sm">
  <div class="max-w-6xl mx-auto px-4">
    <div class="flex justify-between items-center h-16">
      <!-- Мобильное меню -->
      <div class="md:hidden">
        <button id="menuBtn" class="p-2 rounded-lg hover:bg-gray-200">
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>

      <!-- Десктопное меню -->
      <div class="hidden md:flex space-x-8">
        <a href="/" class="text-gray-700 hover:text-blue-600 transition-colors">
          Главная
        </a>
        <a
          href="/about"
          class="text-gray-700 hover:text-blue-600 transition-colors"
        >
          О фирме
        </a>
        <a
          href="/services"
          class="text-gray-700 hover:text-blue-600 transition-colors"
        >
          Услуги
        </a>
        <a
          href="/lawyers"
          class="text-gray-700 hover:text-blue-600 transition-colors"
        >
          Юристы
        </a>
        <a
          href="/reviews"
          class="text-gray-700 hover:text-blue-600 transition-colors"
        >
          Отзывы
        </a>
        <a
          href="/courtsessions"
          class="text-gray-700 hover:text-blue-600 transition-colors"
        >
          Сессии
        </a>
        <a
        href="/articles"
        class="text-gray-700 hover:text-blue-600 transition-colors"
      >
        Статьи
      </a>
        <a
          href="/timer"
          class="text-gray-700 hover:text-blue-600 transition-colors"
        >
          Время заседаний
        </a>
      </div>
    </div>

    <!-- Мобильное меню (скрытое по умолчанию) -->
    <div id="mobileMenu" class="hidden md:hidden py-2 space-y-2">
      <a
        href="/about"
        class="block px-4 py-2 text-gray-700 hover:bg-gray-200 rounded"
      >
        О фирме
      </a>
      <!-- ... остальные ссылки ... -->
    </div>
  </div>
</nav>

<script>
  document.getElementById("menuBtn").addEventListener("click", () => {
    const menu = document.getElementById("mobileMenu");
    menu.classList.toggle("hidden");
  });
</script>

