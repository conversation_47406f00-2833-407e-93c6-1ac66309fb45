"use client"

import { useState, useEffect, useRef } from "react"
import { format } from 'date-fns'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Play, Pause, StopCircle } from "lucide-react"
import { toast } from "sonner"

export const CourtTimer = () => {
    const [time, setTime] = useState(0)
    const [isRunning, setIsRunning] = useState(false)
    const [sessionName, setSessionName] = useState("")
    const [sessionNumber, setSessionNumber] = useState("")
    const [startTime, setStartTime] = useState<Date | null>(null);
    const timerRef = useRef<NodeJS.Timeout>()

    useEffect(() => {
        if (isRunning) {
            timerRef.current = setInterval(() => {
                setTime((prev) => prev + 1)
            }, 1000)
        } else {
            clearInterval(timerRef.current)
        }

        return () => clearInterval(timerRef.current)
    }, [isRunning])

    const formatTime = (seconds: number) => {
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        const remainingSeconds = seconds % 60

        return `${hours.toString().padStart(2, "0")}:${minutes
            .toString()
            .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`
    }

    const handleStart = () => {
        if (!sessionName || !sessionNumber) {
            toast.error("Пожалуйста, введите название и номер заседания")
            return
        }
        setStartTime(new Date());
        setIsRunning(true)
    }

    const handlePause = () => {
        setIsRunning(false)
    }

    const handleEnd = async () => {
        if (time === 0) {
            toast.error("Таймер не был запущен")
            return
        }

        setIsRunning(false)

        const endTimestamp = new Date()
        console.log('startTime:', startTime)


        if (!startTime) return

        const startDateTime = format(startTime, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx");

        const courtSession = {
            time: time,
            sessionName: sessionName,
            sessionNumber: sessionNumber,
            startTime: startDateTime,
            endTime: format(endTimestamp, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx")
        }


        console.log(courtSession)

        try {

            await fetch('/api/courtsession', {
                method: 'POST',
                body: JSON.stringify(courtSession)
            });

            toast.success("Данные заседания сохранены")
            setSessionNumber("")
        } catch (error) {
            toast.error("Ошибка при сохранении данных")
        }
    }

    return (
        <Card className="w-full max-w-md mx-auto">
            <CardHeader>
                <CardTitle className="text-center">Таймер судебного заседания</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="session-name">Название заседания</Label>
                    <Input
                        id="session-name"
                        value={sessionName}
                        onChange={(e) => setSessionName(e.target.value)}
                        placeholder="Введите название заседания"
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="session-number">Номер заседания</Label>
                    <Input
                        id="session-number"
                        value={sessionNumber}
                        onChange={(e) => setSessionNumber(e.target.value)}
                        placeholder="Введите номер заседания"
                    />
                </div>
                <div className="flex justify-center">
                    <div className="text-4xl font-mono font-bold tabular-nums" role="timer" aria-label="Таймер заседания">
                        {formatTime(time)}
                    </div>
                </div>
            </CardContent>
            <CardFooter className="flex justify-center gap-4">
                {!isRunning ? (
                    <Button onClick={handleStart} className="w-24">
                        <Play className="mr-2 h-4 w-4" />
                        Начать
                    </Button>
                ) : (
                    <Button onClick={handlePause} className="w-24">
                        <Pause className="mr-2 h-4 w-4" />
                        Пауза
                    </Button>
                )}
                <Button onClick={handleEnd} variant="destructive" className="w-24">
                    <StopCircle className="mr-2 h-4 w-4" />
                    Завершить
                </Button>
            </CardFooter>
        </Card>
    )
}

