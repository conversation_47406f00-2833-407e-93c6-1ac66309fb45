{"name": "labamain", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/node": "^9.1.0", "@astrojs/react": "^4.2.0", "@prisma/client": "^6.4.1", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@ramonak/react-progress-bar": "^5.3.0", "@tailwindcss/vite": "^4.0.8", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "astro": "^5.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide": "^0.475.0", "lucide-react": "^0.475.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.0.8", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^22.13.5", "prettier": "^3.5.2", "prettier-plugin-astro": "^0.14.1", "prisma": "^6.4.1"}}