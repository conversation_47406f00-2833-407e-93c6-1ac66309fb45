// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Review {
  id          Int      @id @default(autoincrement())
  clientName  String
  rating      Int      @default(0)
  comment     String
  createdAt   DateTime @default(now())
  approved    Boolean  @default(false) // Модерация отзывов
}

model Consultation {
  id          Int      @id @default(autoincrement())
  name        String
  phone       String
  text        String

  createdAt   DateTime @default(now())
}

model CourtSession {
  id          Int      @id @default(autoincrement())
  sessionNumber String
  sessionName String
  startTime   DateTime
  endTime     DateTime
}

model Article {
  id        Int      @id @default(autoincrement())
  title     String
  content   String
  authorName String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  published <PERSON>olean  @default(false)
}

model Client {
  id          Int      @id @default(autoincrement())
  fullName   String
  phone       String
  email       String?
  createdAt   DateTime @default(now())
  cazes       Caze[]
}

model Caze {
  id          Int      @id @default(autoincrement())
  title       String
  description String
  clientId    Int
  client      Client   @relation(fields: [clientId], references: [id])
}


